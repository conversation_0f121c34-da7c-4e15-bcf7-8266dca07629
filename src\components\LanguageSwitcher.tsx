import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronDown } from 'lucide-react';

interface Language {
  code: string;
  name: string;
  flag: React.ReactNode;
}

// Flag components using CSS-based flag representations
const FlagUS: React.FC<{ className?: string }> = ({ className = "w-6 h-4" }) => (
  <div className={`${className} relative overflow-hidden rounded-sm border border-gray-200`}>
    <div className="absolute inset-0 bg-gradient-to-b from-red-600 via-red-600 to-red-600">
      <div className="absolute inset-0 bg-gradient-to-b from-red-600 via-white to-red-600" style={{
        backgroundImage: `repeating-linear-gradient(0deg, #B91C1C 0px, #B91C1C 3.08px, white 3.08px, white 6.15px)`
      }}>
        <div className="absolute top-0 left-0 w-2/5 h-7/13 bg-blue-800"></div>
      </div>
    </div>
  </div>
);

const FlagFR: React.FC<{ className?: string }> = ({ className = "w-6 h-4" }) => (
  <div className={`${className} relative overflow-hidden rounded-sm border border-gray-200 flex`}>
    <div className="w-1/3 bg-blue-700"></div>
    <div className="w-1/3 bg-white"></div>
    <div className="w-1/3 bg-red-600"></div>
  </div>
);

const FlagNL: React.FC<{ className?: string }> = ({ className = "w-6 h-4" }) => (
  <div className={`${className} relative overflow-hidden rounded-sm border border-gray-200`}>
    <div className="h-full bg-gradient-to-b from-red-600 via-white to-blue-700">
      <div className="h-1/3 bg-red-600"></div>
      <div className="h-1/3 bg-white"></div>
      <div className="h-1/3 bg-blue-700"></div>
    </div>
  </div>
);

const languages: Language[] = [
  { code: 'en', name: 'English', flag: <FlagUS /> },
  { code: 'fr', name: 'Français', flag: <FlagFR /> },
  { code: 'nl', name: 'Nederlands', flag: <FlagNL /> },
];

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-nova-red focus:ring-opacity-50"
        aria-label="Select language"
      >
        <div className="flex items-center">
          {currentLanguage.flag}
        </div>
        <span className="sm:inline text-sm font-medium text-gray-700">
          {currentLanguage.name}
        </span>
        <ChevronDown
          size={16}
          className={`text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
            <div className="py-1">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageChange(language.code)}
                  className={`w-full flex items-center space-x-3 px-4 py-2 text-sm hover:bg-gray-50 transition-colors duration-200 ${
                    currentLanguage.code === language.code
                      ? 'bg-nova-red bg-opacity-10 text-gray-700'
                      : 'text-gray-700'
                  }`}
                >
                  <div className="flex items-center">
                    {language.flag}
                  </div>
                  <span className="font-medium">{language.name}</span>
                  {currentLanguage.code === language.code && (
                    <span className="ml-auto text-nova-red">✓</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageSwitcher;
